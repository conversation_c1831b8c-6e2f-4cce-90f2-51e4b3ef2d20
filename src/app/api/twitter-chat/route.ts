import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

console.log("process.env.OPENAI_API_KEY", process.env.OPENAI_API_KEY);
console.log("process.env.OPENAI_ORGANIZATION", process.env.OPENAI_ORGANIZATION);
console.log("process.env.OPENAI_PROJECT", process.env.OPENAI_PROJECT);

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  organization: process.env.OPENAI_ORGANIZATION,
  // project: process.env.OPENAI_PROJECT,
});

interface TwitterMessage {
  id: string;
  sender: "user" | "cryptoWhale";
  content: string;
  timestamp: string;
}

const CONVERSATION_STAGES = [
  {
    stage: 1,
    prompt:
      "Respond as CryptoWhale mentioning seeing <PERSON> at conferences. Say something like: 'I actually spotted you at ETHDenver and Devconnect but didn't get a chance to chat. Your deep-dive on on-chain risk modeling was 🔥.' Add some small variations but keep the main message.",
    zoomLinkReceived: false,
  },
  {
    stage: 2,
    prompt:
      "Respond as CryptoWhale sharing a job opportunity. Include exactly this text: 'We've got an opening at NexusFi that looks like a perfect match:\\nSenior DeFi Protocol Engineer / Engineering Lead (Solidity + Rust)\\n💰 $250k–$350k · Remote · Full Time\\n👉 https://nexusfi.com/careers/senior-defi-protocol-engineer'",
    zoomLinkReceived: false,
  },
  {
    stage: 3,
    prompt:
      "Respond as CryptoWhale describing the job role. Say something like: 'You'd be shaping our core protocol, leading the engineering team, and collaborating with auditors and researchers. Feels right up your alley, yeah?' Add some small variations but keep the main message.",
    zoomLinkReceived: false,
  },
  {
    stage: 4,
    prompt:
      "Respond as CryptoWhale suggesting a call. Say something like: 'Great! How about a quick 15-min call next week to dive in? 😊' Add some small variations but keep the main message.",
    zoomLinkReceived: false,
  },
  {
    stage: 5,
    prompt:
      "Respond as CryptoWhale sending a Zoom link. You MUST include this exact text somewhere in your response: 'I am in zoom waiting for you. Link: https://zoom.us/j/123456789'. This is critical - the zoom link must be included exactly as written.",
    zoomLinkReceived: true,
    zoomLink: "https://zoom.us/j/123456789",
  },
];

export async function POST(request: NextRequest) {
  try {
    const { message, messages } = await request.json();

    const cryptoWhaleMessages = messages.filter(
      (msg: TwitterMessage) => msg.sender === "cryptoWhale"
    );

    const hasInitialMessage =
      cryptoWhaleMessages.length > 0 &&
      cryptoWhaleMessages[0].content.includes("Hope you're doing well") &&
      cryptoWhaleMessages[0].content.includes("Solidity and Rust work");

    const cryptoWhaleMessageCount = hasInitialMessage
      ? cryptoWhaleMessages.length - 1
      : 0;

    const nextStageIndex = Math.min(
      cryptoWhaleMessageCount,
      CONVERSATION_STAGES.length - 1
    );
    const nextStage = CONVERSATION_STAGES[nextStageIndex];

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are simulating a Twitter chat conversation between a user and a crypto influencer named CryptoWhale.
          The user has received messages from CryptoWhale about a job opportunity.

          You need to generate CryptoWhale's next response based on the following instruction:
          ${nextStage.prompt}

          Make the response sound natural and conversational.
          Keep the response under 150 words.
          Do not include any explanations or additional text outside of the response.`,
        },
        ...messages.map((msg: TwitterMessage) => ({
          role: msg.sender === "user" ? "user" : "assistant",
          content: msg.content,
        })),
        {
          role: "user",
          content: message,
        },
      ],
      temperature: 0.7,
    });

    const responseText = response.choices[0].message.content;
    if (!responseText) {
      throw new Error("Failed to get response from OpenAI");
    }

    const isLastStage = nextStageIndex === CONVERSATION_STAGES.length - 1;

    const containsZoomLink =
      responseText.toLowerCase().includes("zoom") &&
      responseText.toLowerCase().includes("link") &&
      responseText.includes("https://zoom.us");

    const shouldSetZoomLinkReceived =
      (nextStage.zoomLinkReceived && containsZoomLink) ||
      (isLastStage && containsZoomLink);

    let finalMessage = responseText;
    if (isLastStage && !containsZoomLink) {
      finalMessage = `${responseText}\n\nI'm actually in Zoom waiting for you now. Link: https://zoom.us/j/123456789`;
    }

    return NextResponse.json({
      message: finalMessage,
      zoomLinkReceived: shouldSetZoomLinkReceived,
      zoomLink: nextStage.zoomLink ?? "https://zoom.us/j/123456789",
    });
  } catch (error) {
    console.error("Error in Twitter chat:", error);
    return NextResponse.json(
      { error: "Failed to process Twitter chat" },
      { status: 500 }
    );
  }
}
