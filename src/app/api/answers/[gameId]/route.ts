import { NextRequest, NextResponse } from "next/server";
import { evaluationCriteria } from "@/lib/evaluation-criteria";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const gameId = pathParts[pathParts.length - 1];

    const criteria =
      evaluationCriteria[gameId as keyof typeof evaluationCriteria];
    if (!criteria) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    // Return all expected answers organized by question ID
    const allAnswers: Record<number, string[]> = {};
    criteria.questions.forEach((question) => {
      allAnswers[question.id] = question.expectedAnswers;
    });

    return NextResponse.json(allAnswers);
  } catch (error) {
    console.error("Error fetching answers:", error);
    return NextResponse.json(
      { error: "Failed to fetch answers" },
      { status: 500 }
    );
  }
}
