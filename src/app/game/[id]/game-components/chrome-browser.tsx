import type React from "react";
import { useChrome } from "./chrome-context";

interface Tab {
  name: string;
  url: string;
  content: React.ReactNode;
  closable?: boolean;
}

interface Extension {
  id: string;
  name: string;
  icon: string;
  content: React.ReactNode;
  active: boolean;
}

interface ChromeBrowserMockProps {
  url?: string;
  children: React.ReactNode;
  className?: string;
  tabs?: Tab[];
  extensions?: Extension[];
}

export const ChromeBrowserMock: React.FC<ChromeBrowserMockProps> = ({
  url = "",
  children,
  className = "",
}) => {
  const {
    tabs,
    activeTabIndex,
    setActiveTabIndex,
    closeTab,
    extensions,
    activeExtension,
    toggleExtension,
  } = useChrome();

  const defaultTab = {
    name: "New Tab",
    url: url,
    content: children,
    closable: false,
  };

  const activeTab = tabs.length > 0 ? tabs[activeTabIndex] : defaultTab;

  const activeExtensionContent = activeExtension
    ? extensions.find((ext) => ext.id === activeExtension)?.content
    : null;

  return (
    <div
      className={` overflow-hidden border border-gray-200 shadow-lg h-full flex flex-col ${className}`}
    >
      <div className="bg-gray-100 px-4 py-2 flex-shrink-0">
        <div className="flex items-center overflow-x-auto mb-2">
          {tabs.map((tab, index) => (
            <div
              key={`tab-${tab.name}-${index}`}
              onClick={() => setActiveTabIndex(index)}
              className={`flex items-center ${
                index === activeTabIndex
                  ? "bg-white"
                  : "bg-gray-200 hover:bg-gray-100"
              } rounded-t-lg px-3 py-1 mr-1 border-b-0 border border-gray-200 cursor-pointer`}
            >
              <div className="w-3 h-3 rounded-full bg-gray-300 mr-2"></div>
              <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
                {tab.name}
              </span>
              {tab.closable && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeTab(index);
                  }}
                  className="ml-2 text-gray-500 hover:text-gray-700 rounded-full w-4 h-4 flex items-center justify-center cursor-pointer"
                >
                  <span className="text-xs">×</span>
                </button>
              )}
            </div>
          ))}
        </div>

        <div className="flex items-center">
          <div className="flex-1 flex gap-1">
            <div className="bg-white rounded-full px-3 py-1 text-sm flex items-center grow-1">
              <div className="flex-shrink-0 w-4 h-4 mr-2 text-gray-400">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                </svg>
              </div>
              <span className="text-gray-600 truncate flex-1">
                {activeTab.url}
              </span>
            </div>
            <div className="flex items-center space-x-2 ml-2">
              {extensions.map((extension) => (
                <div
                  key={extension.id}
                  className={`w-6 h-6 rounded-md flex items-center justify-center cursor-pointer ${
                    activeExtension === extension.id ? "bg-gray-200" : ""
                  }`}
                  onClick={() => toggleExtension(extension.id)}
                  title={extension.name}
                >
                  <img
                    src={extension.icon}
                    alt={extension.name}
                    className="w-5 h-5 object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white flex-1 overflow-auto relative">
        {activeTab.content}

        {activeExtension &&
          activeExtensionContent &&
          (() => {
            const activeExt = extensions.find(
              (ext) => ext.id === activeExtension
            );

            return (
              <div className="absolute top-0 right-0 z-50 w-[375px] shadow-xl border border-gray-300 rounded-lg overflow-hidden">
                <div className="bg-gray-100 px-3 py-2 flex justify-between items-center border-b border-gray-300">
                  <div className="flex items-center">
                    {activeExt?.icon && (
                      <div className="w-5 h-5 mr-2 relative">
                        <div
                          className="w-full h-full bg-contain bg-center bg-no-repeat"
                          style={{ backgroundImage: `url(${activeExt.icon})` }}
                        />
                      </div>
                    )}
                    <span className="font-medium text-sm">
                      {activeExt?.name || "Extension"}
                    </span>
                  </div>
                  <button
                    onClick={() => toggleExtension(activeExtension)}
                    className="text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointer w-6 h-6 flex items-center justify-center"
                  >
                    ×
                  </button>
                </div>
                <div className="max-h-[500px] overflow-y-auto bg-white">
                  {activeExtensionContent}
                </div>
              </div>
            );
          })()}
      </div>
    </div>
  );
};
