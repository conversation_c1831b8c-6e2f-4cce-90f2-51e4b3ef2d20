import React from "react";
import { HelpCircle, AudioWaveformIcon as Waveform } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChatInputProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onGetHint: () => void;
  isDisabled: boolean;
  isButtonDisabled: boolean;
  isHintDisabled?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  onInputChange,
  onSubmit,
  onGetHint,
  isDisabled,
  isButtonDisabled,
  isHintDisabled = false,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && e.shiftKey) {
      // Shift+Enter: Submit the form
      e.preventDefault();
      if (!isButtonDisabled) {
        onSubmit(e as any);
      }
    }
    // Regular Enter: Allow default behavior (new line)
  };

  return (
    <div className="p-3 bg-[#343541] border-t border-gray-700">
      <div className="flex gap-2 mb-3">
        <Button
          onClick={onGetHint}
          variant="outline"
          size="sm"
          disabled={isHintDisabled}
          className={cn(
            "bg-yellow-600/20 border-yellow-600/50 text-yellow-400",
            isHintDisabled
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-yellow-600/30"
          )}
        >
          <HelpCircle className="w-4 h-4 mr-1" />
          {isHintDisabled ? "Hint Used" : "Hint"}
        </Button>
      </div>

      <form onSubmit={onSubmit} className="relative">
        <textarea
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isDisabled}
          placeholder="Type your answer... (Shift+Enter to submit)"
          rows={3}
          className={cn(
            "w-full py-3 px-4 pr-16 rounded-lg text-white placeholder-gray-400 outline-none resize-none",
            isDisabled ? "bg-[#2a2b36] cursor-not-allowed" : "bg-[#40414f]"
          )}
        />
        <div className="absolute bottom-0 right-0 flex items-center h-full px-3 space-x-2 text-gray-400">
          <button
            type="submit"
            className={cn(
              "p-1 rounded",
              isButtonDisabled
                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                : "bg-white/10 hover:bg-gray-600 text-white cursor-pointer"
            )}
            disabled={isButtonDisabled}
          >
            <Waveform size={18} />
          </button>
        </div>
      </form>
    </div>
  );
};
